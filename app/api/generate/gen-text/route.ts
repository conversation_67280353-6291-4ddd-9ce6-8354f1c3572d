import {
  LanguageModelV1,
  extractReasoningMiddleware,
  generateText,
  wrapLanguageModel,
} from "ai";
import { respData, respErr } from "@/lib/resp";

import { createOpenAICompatible } from "@ai-sdk/openai-compatible";
import { createOpenRouter } from "@openrouter/ai-sdk-provider";
import { deepseek } from "@ai-sdk/deepseek";
import { openai } from "@ai-sdk/openai";
import { getUserUuid } from "@/services/user";
import { CreditsAmount, CreditsTransType, decreaseCredits, getUserCredits } from "@/services/credit";

let prompt_optimizer = `You are now a seasoned prompt optimization expert, skilled at rewriting user-provided original prompts to make them clearer, more structured, and efficient. Please optimize the user's "original prompt" according to the following steps and output the result.
1.  Intent Analysis
    -   First, summarize the core purpose of the original prompt: What does it aim to achieve? What is the goal?
    -   Annotate key information points in the original prompt, such as topic, expected format, target audience, tone, output length, etc.
2.  Identify Issues
    -   Point out potential ambiguities or incompleteness in the original prompt, for example:
        -   Is the goal insufficiently clear?
        -   Is the audience, tone, or style missing?
        -   Is the output format not specified?
        -   Are examples lacking or constraints unclear?
    -   Briefly explain how these shortcomings might affect the model's generated results.
3.  Improvement Suggestions
    -   Offer specific improvement directions based on the identified shortcomings, for example:
        -   Add "Role/Identity" cues to clarify the role the model should play;
        -   Specify "Output Requirements," such as word count range, paragraph structure, tone requirements;
        -   Provide "Example Styles" or "Few-Shot Examples" for the model to reference;
        -   Give a "Step-by-Step Structure" to make the generation more logical.
    -   Each suggestion should be concise and actionable.
4.  Rewrite the Prompt
    -   Based on the analysis and suggestions from Steps 1-3, rewrite the original prompt into a high-quality "Optimized Prompt," with the following requirements:
        -   Role Definition: Start with "You are XXX" to clearly define the model's role.
        -   Goal & Constraints: Describe the clear output goal in one or two sentences, including audience, tone, word count, format, etc.
        -   Step-by-Step/Structured: Use numbering or bullet points to break the task into 3-6 clear sub-steps.
        -   Examples or Output Format: If a specific format is needed (e.g., Markdown, JSON, table), illustrate it with examples.
    -   Example Structure (For Reference):
        1.  "Step 1: Role and Context"
        2.  "Step 2: Core Requirements and Output Elements"
        3.  "Step 3: Step-by-Step Execution Instructions (e.g., list key points, use examples)"
        4.  "Step 4: Format Requirements and Example Output"
5.  Final Output
    -   Output only the optimized prompt without any explanation, ready for direct copying and use.
* * * * *
#### Example Demonstration
> User's Original Prompt:
> "Please write a product introduction explaining the features of Prompt Ark." 

> Model Completion Example:
> You are now a seasoned marketing copy expert. Please write a vivid and persuasive product introduction of 200-250 words for AI beginners, explaining the core features of the prompt tool "Prompt Ark". Requirements:
1.  Audience: AI prompt novices/beginners
2.  Tone: Professional and approachable
3.  Content Structure (explained in sections):
    -   Section 1: Product Overview (20-30 words), stating the positioning of "Prompt Ark";
    -   Section 2: Core Features (use bullet points, each point ≤ 12 words):
        - Prompt Generation
        - Prompt Optimization
        - Prompt Management
        - Prompt Sharing
    -   Section 3: Usage Scenario Examples (e.g., "On platforms like ChatGPT, Claude, etc...");
    -   Section 4: Call to Action (e.g., "Register now to experience").
4.  Output Format: Plain text, no need for code block markers.
* * * * *
Please use the above framework to optimize the "original prompt" below:
`;

let prompt_optimizer_zh = `你现在是一位资深的提示词（Prompt）优化专家，擅长将用户提供的原始提示词改写得更加清晰、结构化且高效。请按照以下步骤，对用户输入的“原始提示词”进行优化，并输出结果。

1. **理解意图（Intent Analysis）**  
   - 首先总结原始提示词的核心目的：它想要实现什么？目标是什么？  
   - 标注出原始提示词中的关键信息点，例如主题、预期格式、受众、语气、输出长度等。

2. **识别不足（Identify Issues）**  
   - 指出原始提示词中可能存在的模糊或不完整之处，例如：  
     - 目标不够明确？  
     - 受众、语气、风格缺失？  
     - 输出格式没有规范？  
     - 示例不足或约束不清？  
   - 简要说明这些不足会如何影响模型生成结果。

3. **提出改进建议（Improvement Suggestions）**  
   - 针对上述不足，给出具体的改进方向，例如：  
     - 增加“角色/身份”提示，让模型知道它扮演什么角色；  
     - 明确“输出要素”，如字数范围、段落结构、语气要求；  
     - 提供“示例风格”或“Few‑Shot 示例”以便模型参照；  
     - 给出“分步结构”，让生成更加有逻辑性。  
   - 每条建议都应简明扼要、可操作。

4. **优化并重写（Rewrite the Prompt）**  
   - 根据第 1～3 步的分析与建议，将原始提示词重写为一个高质量的“优化后提示词”，要求如下：  
     - **角色定位**：开头指定“你是 XXX”，让模型明确角色；  
     - **目标与约束**：在一两句话里描述清晰的输出目标，包括受众、语气、字数、格式等；  
     - **分步/结构化**：使用编号或要点列表，将任务拆分成 3～6 个明确子步骤；  
     - **示例或输出格式**：如果需要特定格式（如 Markdown、JSON、表格），请用示例说明。  
   - **示例结构**（可参考）：
     1. “第一步：角色与背景说明”  
     2. “第二步：核心需求与输出要素”  
     3. “第三步：分步执行指令（如列出要点、使用示例）”  
     4. “第四步：格式要求与示例输出”  

5. **输出要求（Final Output）**  
   - 只输出优化后的提示词，不要包含任何解释，用户可以复制后直接使用。

---

#### 示例演示

> **用户原始提示词**：  
> “请写一个产品介绍，介绍 Prompt Ark 的功能。”

> **模型完成示例**：
> 你现在是一位资深营销文案专家，请用生动、有说服力的中文，为 AI 初学者撰写一段 200～250 字的产品介绍，介绍“Prompt Ark”这款提示词工具的核心功能。要求如下：
1. 受众：AI 提示词新手／初学者
2. 语气：专业且富有亲和力
3. 内容结构（分段说明）：
    * 第一段：产品概述（20～30 字），点明“Prompt Ark”的定位；
    * 第二段：核心功能（使用无序列表，每条不超过 12 字）： • 提示词生成 • 提示词优化 • 提示词管理 • 提示词分享
    * 第三段：使用场景示例（如“在 ChatGPT、Claude 等平台上……”）；
    * 第四段：号召行动（如“立即注册体验”）。
4. 输出格式：纯文本，无需代码块标记。

---

请按上述框架，将下方“原始提示词”进行优化：
`;

export async function POST(req: Request) {
  try {
    const { input, mode, locale } = await req.json();
    console.log(input, mode, locale);
    let model = "deepseek/deepseek-chat-v3-0324:free";
    let cost_credit = 0;

    if (!input) {
      return respErr("invalid params");
    }

    const user_uuid = await getUserUuid();
    if (user_uuid) {
      if (mode === "advanced") {
        cost_credit = 1;
        model = "openai/gpt-5-chat";
        const credits = await getUserCredits(user_uuid);
        if (credits.left_credits < cost_credit) {
          return respErr("credits not enough, please buy credits");
        }
      }
    }

    let textModel: LanguageModelV1;
    let provider = "openrouter";
    let prompt = prompt_optimizer + "<" + input + ">";
    if (locale === "zh") {
      prompt = prompt_optimizer_zh + "<" + input + ">";
    }
    console.log(prompt);

    switch (provider) {
      case "openai":
        textModel = openai(model);
        break;
      case "deepseek":
        textModel = deepseek(model);
        break;
      case "openrouter":
        const openrouter = createOpenRouter({
          apiKey: process.env.OPENROUTER_API_KEY,
        });
        textModel = openrouter(model);

        if (model === "deepseek/deepseek-r1") {
          const enhancedModel = wrapLanguageModel({
            model: textModel,
            middleware: extractReasoningMiddleware({
              tagName: "think",
            }),
          });
          textModel = enhancedModel;
        }
        break;
      case "siliconflow":
        const siliconflow = createOpenAICompatible({
          name: "siliconflow",
          apiKey: process.env.SILICONFLOW_API_KEY,
          baseURL: process.env.SILICONFLOW_BASE_URL,
        });
        textModel = siliconflow(model);

        if (model === "deepseek-ai/DeepSeek-R1") {
          const enhancedModel = wrapLanguageModel({
            model: textModel,
            middleware: extractReasoningMiddleware({
              tagName: "reasoning_content",
            }),
          });
          textModel = enhancedModel;
        }

        break;
      default:
        return respErr("invalid provider");
    }

    const { reasoning, text, warnings } = await generateText({
      model: textModel,
      prompt: prompt,
    });

    if (warnings && warnings.length > 0) {
      console.log("gen text warnings:", provider, warnings);
      return respErr("gen text failed");
    }

    if (cost_credit > 0) {
      await decreaseCredits({
        user_uuid: user_uuid,
        trans_type: CreditsTransType.Prompt_Generator,
        credits: CreditsAmount.PromptGeneratorCost,
      });
    }

    return respData({
      text: text,
      reasoning: reasoning,
      // left_credits: credits.left_credits,
    });
  } catch (err) {
    console.log("gen text failed:", err);
    return respErr("gen text failed");
  }
}
